% Options for packages loaded elsewhere
\PassOptionsToPackage{unicode}{hyperref}
\PassOptionsToPackage{hyphens}{url}
%
\documentclass[
]{article}
\usepackage{amsmath,amssymb}
\usepackage{iftex}
\ifPDFTeX
  \usepackage[T1]{fontenc}
  \usepackage[utf8]{inputenc}
  \usepackage{textcomp} % provide euro and other symbols
\else % if luatex or xetex
  \usepackage{unicode-math} % this also loads fontspec
  \defaultfontfeatures{Scale=MatchLowercase}
  \defaultfontfeatures[\rmfamily]{Ligatures=TeX,Scale=1}
\fi
\usepackage{lmodern}
\ifPDFTeX\else
  % xetex/luatex font selection
\fi
% Use upquote if available, for straight quotes in verbatim environments
\IfFileExists{upquote.sty}{\usepackage{upquote}}{}
\IfFileExists{microtype.sty}{% use microtype if available
  \usepackage[]{microtype}
  \UseMicrotypeSet[protrusion]{basicmath} % disable protrusion for tt fonts
}{}
\makeatletter
\@ifundefined{KOMAClassName}{% if non-KOMA class
  \IfFileExists{parskip.sty}{%
    \usepackage{parskip}
  }{% else
    \setlength{\parindent}{0pt}
    \setlength{\parskip}{6pt plus 2pt minus 1pt}}
}{% if KOMA class
  \KOMAoptions{parskip=half}}
\makeatother
\usepackage{xcolor}
\usepackage[margin=1in]{geometry}
\usepackage{graphicx}
\makeatletter
\newsavebox\pandoc@box
\newcommand*\pandocbounded[1]{% scales image to fit in text height/width
  \sbox\pandoc@box{#1}%
  \Gscale@div\@tempa{\textheight}{\dimexpr\ht\pandoc@box+\dp\pandoc@box\relax}%
  \Gscale@div\@tempb{\linewidth}{\wd\pandoc@box}%
  \ifdim\@tempb\p@<\@tempa\p@\let\@tempa\@tempb\fi% select the smaller of both
  \ifdim\@tempa\p@<\p@\scalebox{\@tempa}{\usebox\pandoc@box}%
  \else\usebox{\pandoc@box}%
  \fi%
}
% Set default figure placement to htbp
\def\fps@figure{htbp}
\makeatother
\setlength{\emergencystretch}{3em} % prevent overfull lines
\providecommand{\tightlist}{%
  \setlength{\itemsep}{0pt}\setlength{\parskip}{0pt}}
\setcounter{secnumdepth}{-\maxdimen} % remove section numbering
\usepackage{graphicx}
\usepackage{float}
\usepackage{amsmath}
\usepackage{bookmark}
\IfFileExists{xurl.sty}{\usepackage{xurl}}{} % add URL line breaks if available
\urlstyle{same}
\hypersetup{
  hidelinks,
  pdfcreator={LaTeX via pandoc}}

\author{}
\date{\vspace{-2.5em}}

\begin{document}

Test passed Test passed

\begin{verbatim}
## Test passed
\end{verbatim}

\section{Question}\label{question}

Antonio y Francisco viven en un apartamento y comparten el pago de los
gastos. En octubre, el consumo de gas natural se facturó por \(15.500\),
incluido el cargo fijo de \(2.500\), que es el mismo todos los meses. La
gráfica muestra el consumo histórico en metros cúbicos de ese servicio.

\includegraphics[width=0.8\linewidth,height=\textheight,keepaspectratio]{grafico_consumo_gas.png}

Un hogar puede consumir máximo 18 metros cúbicos en un mes. ¿A qué
porcentaje del consumo máximo posible corresponde el consumo de junio?

\subsection{Answerlist}\label{answerlist}

\begin{itemize}
\tightlist
\item
  72\%
\item
  11\%
\item
  61\%
\item
  85\%
\end{itemize}

\section{Solution}\label{solution}

\subsubsection{Análisis del problema}\label{anuxe1lisis-del-problema}

Este problema requiere \textbf{interpretación de gráficos de barras} y
\textbf{cálculo de porcentajes}. Los pasos son:

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\tightlist
\item
  \textbf{Leer correctamente la gráfica} para identificar el consumo de
  junio
\item
  \textbf{Identificar el consumo máximo posible} según el enunciado
\item
  \textbf{Aplicar la fórmula de porcentaje} para encontrar la relación
\end{enumerate}

\subsubsection{Paso 1: Lectura de la
gráfica}\label{paso-1-lectura-de-la-gruxe1fica}

Observando la gráfica de consumo histórico de Antonio y Francisco:

\textbf{Consumos mensuales registrados:}

\begin{itemize}
\tightlist
\item
  Abril: 12 metros cúbicos
\item
  Mayo: 12 metros cúbicos
\item
  Junio: 11 metros cúbicos
\item
  Julio: 12 metros cúbicos
\item
  Agosto: 13 metros cúbicos
\item
  Septiembre: 12 metros cúbicos
\item
  Octubre: 15 metros cúbicos
\end{itemize}

\textbf{El consumo de junio fue de 11 metros cúbicos.}

\subsubsection{Paso 2: Datos del
problema}\label{paso-2-datos-del-problema}

\begin{itemize}
\tightlist
\item
  \textbf{Consumo máximo posible:} 18 metros cúbicos (dato del
  enunciado)
\item
  \textbf{Consumo real de junio:} 11 metros cúbicos (dato de la gráfica)
\end{itemize}

\subsubsection{Paso 3: Aplicación de la fórmula de
porcentaje}\label{paso-3-aplicaciuxf3n-de-la-fuxf3rmula-de-porcentaje}

Para calcular qué porcentaje representa el consumo de junio respecto al
máximo posible:

\[\text{Porcentaje} = \frac{\text{Valor observado}}{\text{Valor máximo}} \times 100\%\]

Sustituyendo los valores:

\[\text{Porcentaje} = \frac{11}{18} \times 100\% = 61\%\]

\subsubsection{Verificación y análisis de
distractores}\label{verificaciuxf3n-y-anuxe1lisis-de-distractores}

\textbf{Respuesta correcta:} 61\%

\textbf{Análisis de errores conceptuales comunes:}

\begin{itemize}
\tightlist
\item
  \textbf{11\%}: Error conceptual - confundir el valor absoluto (11 m³)
  con el porcentaje
\item
  \textbf{72\%}: Error de lectura - usar el consumo de Agosto en lugar
  de junio
\item
  \textbf{85\%}: Error de cálculo - aplicar incorrectamente la fórmula
  de porcentaje
\end{itemize}

\subsubsection{Conclusión}\label{conclusiuxf3n}

El consumo de junio (11 metros cúbicos) representa el \textbf{61\%} del
consumo máximo posible (18 metros cúbicos).

Esta respuesta es coherente porque:

\begin{itemize}
\tightlist
\item
  Se basa en una lectura correcta de la gráfica
\item
  Aplica correctamente la fórmula de porcentaje
\item
  El resultado está dentro del rango esperado (0\% a 100\%)
\end{itemize}

\subsection{Answerlist}\label{answerlist-1}

\begin{itemize}
\tightlist
\item
  Incorrecto
\item
  Incorrecto
\item
  Correcto
\item
  Incorrecto
\end{itemize}

\section{Meta-information}\label{meta-information}

exname: Consumo Gas Natural Porcentaje Máximo extype: schoice
exsolution: 0010 exshuffle: TRUE exsection:
Estadística\textbar Interpretación de
gráficos\textbar Porcentajes\textbar Análisis de datos
exextra{[}Type{]}: Cálculo exextra{[}Program{]}: R
exextra{[}Language{]}: es exextra{[}Level{]}: 2
exextra{[}Competencia{]}: Interpretación y representación
exextra{[}Componente{]}: Aleatorio y sistemas de datos
exextra{[}Contexto{]}: Familiar exextra{[}Dificultad{]}: Media

\end{document}
