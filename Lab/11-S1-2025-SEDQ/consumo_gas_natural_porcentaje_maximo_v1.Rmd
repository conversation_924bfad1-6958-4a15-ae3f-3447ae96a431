---
tipo_pregunta: "Selección múltiple con única respuesta"
competencia: "Interpretación y representación"
componente: "Aleatorio y sistemas de datos"
afirmacion: "Interpreta información presentada en tablas y gráficos"
nivel_dificultad: "Medio"
tiempo_estimado: "3 minutos"
autor: "Sistema R-Exams ICFES"
version: "1.0"
output:
  html_document: default
  pdf_document:
    keep_tex: true
  word_document: default
---

```{r setup, echo=FALSE, results="hide", warning=FALSE, message=FALSE}
# Configuración inicial
library(exams)
library(ggplot2)
library(knitr)

typ <- match_exams_device()
options(scipen = 999)
knitr::opts_chunk$set(
  warning = FALSE,
  message = FALSE,
  echo = FALSE,
  fig.showtext = FALSE,
  fig.cap = "",
  fig.keep = 'all',
  dev = c("png", "pdf"),
  dpi = 150,
  fig.pos = "H"
)
```

```{r generar_datos, echo=FALSE}
# Generar datos aleatorios para el problema
set.seed(sample(1:10000, 1))

# Nombres aleatorios para los personajes
nombres_masculinos <- c("<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>")
nombre<PERSON>_par<PERSON> <- list(
  c("<PERSON>", "<PERSON>"), c("<PERSON>", "<PERSON>"), c("<PERSON>", "<PERSON>"), 
  c("<PERSON>", "<PERSON>"), c("<PERSON>", "<PERSON>"), c("<PERSON>", "<PERSON>")
)
nombre<PERSON>_selecci<PERSON><PERSON> <- sample(nombres_pares, 1)[[1]]
nombre1 <- nombres_seleccionados[1]
nombre2 <- nombres_seleccionados[2]

# Generar consumo máximo posible (variable)
consumo_maximo <- sample(c(18, 20, 22, 25), 1)

# Generar datos de consumo mensual (en metros cúbicos)
# Asegurar que junio tenga un valor que permita un porcentaje "limpio"
porcentajes_objetivo <- c(60, 65, 70, 75, 80, 85, 90)
porcentaje_junio <- sample(porcentajes_objetivo, 1)
consumo_junio <- round((porcentaje_junio * consumo_maximo) / 100)

# Generar consumos para otros meses (variando alrededor de valores realistas)
meses <- c("Abril", "Mayo", "Junio", "Julio", "Agosto", "Septiembre", "Octubre")
consumos_base <- c(11, 12, consumo_junio, 11, 11, 12, 13)

# Añadir variación aleatoria a los otros meses (excepto junio)
for(i in c(1,2,4,5,6,7)) {
  variacion <- sample(-2:2, 1)
  consumos_base[i] <- max(8, min(consumo_maximo-2, consumos_base[i] + variacion))
}

consumos <- consumos_base
names(consumos) <- meses

# Generar datos económicos
cargo_fijo <- sample(c(2500, 2700, 2800, 3000), 1)
total_factura <- sample(c(14000, 14500, 15000, 15500), 1)

# Calcular respuesta correcta
porcentaje_correcto <- round((consumo_junio / consumo_maximo) * 100)

# Generar distractores plausibles
distractores <- c()

# Distractor 1: Usar el consumo de junio como porcentaje directo
if(consumo_junio <= 100 && !consumo_junio %in% distractores) {
  distractores <- c(distractores, consumo_junio)
}

# Distractor 2: Calcular porcentaje con otro mes
otro_mes_idx <- sample(which(names(consumos) != "Junio"), 1)
porcentaje_otro_mes <- round((consumos[otro_mes_idx] / consumo_maximo) * 100)
if(!porcentaje_otro_mes %in% c(porcentaje_correcto, distractores)) {
  distractores <- c(distractores, porcentaje_otro_mes)
}

# Distractor 3: Error de cálculo común (usar denominador incorrecto)
porcentaje_error <- round((consumo_junio / (consumo_maximo - 5)) * 100)
if(porcentaje_error <= 100 && !porcentaje_error %in% c(porcentaje_correcto, distractores)) {
  distractores <- c(distractores, porcentaje_error)
}

# Completar distractores si es necesario
opciones_adicionales <- c(15, 25, 35, 45, 55, 65, 85, 95, 100)
for(opcion in opciones_adicionales) {
  if(length(distractores) >= 3) break
  if(!opcion %in% c(porcentaje_correcto, distractores)) {
    distractores <- c(distractores, opcion)
  }
}

# Asegurar que tenemos exactamente 3 distractores
distractores <- distractores[1:3]

# Crear vector de opciones
opciones <- c(porcentaje_correcto, distractores)
opciones_texto <- paste0(opciones, "%")

# Crear vector de solución (TRUE para la respuesta correcta)
solucion <- c(TRUE, FALSE, FALSE, FALSE)

# Mezclar opciones
orden_aleatorio <- sample(1:4)
opciones_mezcladas <- opciones_texto[orden_aleatorio]
solucion_mezclada <- solucion[orden_aleatorio]
```

```{r crear_grafico, echo=FALSE, fig.width=8, fig.height=5}
# Crear el gráfico de barras
datos_grafico <- data.frame(
  Mes = factor(meses, levels = meses),
  Consumo = consumos
)

# Crear gráfico con ggplot2
grafico <- ggplot(datos_grafico, aes(x = Mes, y = Consumo)) +
  geom_bar(stat = "identity", fill = "#4CAF50", color = "black", width = 0.7) +
  labs(
    title = "",
    x = "Mes",
    y = "Consumo en metros cúbicos"
  ) +
  theme_minimal() +
  theme(
    axis.text.x = element_text(angle = 45, hjust = 1, size = 10),
    axis.text.y = element_text(size = 10),
    axis.title = element_text(size = 11, face = "bold"),
    panel.grid.major.x = element_blank(),
    panel.grid.minor = element_blank(),
    plot.margin = margin(10, 10, 10, 10)
  ) +
  scale_y_continuous(
    breaks = seq(0, max(consumos) + 2, by = 1),
    limits = c(0, max(consumos) + 1)
  )

print(grafico)
```

Question
========

`r nombre1` y `r nombre2` viven en un apartamento y comparten el pago de los gastos. En octubre, el consumo de gas natural se facturó por $`r format(total_factura, big.mark = ".", decimal.mark = ",")`$, incluido el cargo fijo de $`r format(cargo_fijo, big.mark = ".", decimal.mark = ",")`$, que es el mismo todos los meses. La gráfica muestra el consumo histórico en metros cúbicos de ese servicio.

Un hogar puede consumir máximo `r consumo_maximo` metros cúbicos en un mes. ¿A qué porcentaje del consumo máximo posible corresponde el consumo de junio?

Answerlist
----------
* `r opciones_mezcladas[1]`
* `r opciones_mezcladas[2]`
* `r opciones_mezcladas[3]`
* `r opciones_mezcladas[4]`

Solution
========

### Análisis del problema

Para resolver este problema necesitamos:

1. **Identificar el consumo de junio** según la gráfica
2. **Identificar el consumo máximo posible** según el enunciado  
3. **Calcular el porcentaje** que representa el consumo de junio respecto al máximo

### Paso 1: Lectura de la gráfica

Observando la gráfica de consumo histórico:

- El consumo de junio fue de **`r consumo_junio` metros cúbicos**

### Paso 2: Datos del problema

- Consumo máximo posible: **`r consumo_maximo` metros cúbicos**
- Consumo real de junio: **`r consumo_junio` metros cúbicos**

### Paso 3: Cálculo del porcentaje

Para calcular qué porcentaje representa el consumo de junio respecto al máximo posible:

$$\text{Porcentaje} = \frac{\text{Consumo de junio}}{\text{Consumo máximo}} \times 100\%$$

$$\text{Porcentaje} = \frac{`r consumo_junio`}{`r consumo_maximo`} \times 100\% = `r porcentaje_correcto`\%$$

### Respuesta

El consumo de junio (`r consumo_junio` metros cúbicos) representa el **`r porcentaje_correcto`%** del consumo máximo posible (`r consumo_maximo` metros cúbicos).

```{r solucion, echo=FALSE, results="asis"}
answerlist(ifelse(solucion_mezclada, "Correcto", "Incorrecto"), markup = "markdown")
```

Meta-information
================
exname: consumo_gas_natural_porcentaje_maximo
extype: schoice
exsolution: `r paste(as.integer(solucion_mezclada), collapse="")`
exshuffle: TRUE
exsection: Estadística|Interpretación de gráficos|Porcentajes|Análisis de datos
exextra[Type]: Cálculo
exextra[Program]: R
exextra[Language]: es
exextra[Level]: 2
