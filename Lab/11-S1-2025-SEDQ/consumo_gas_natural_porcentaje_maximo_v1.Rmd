---
output:
  html_document: default
  word_document: default
  pdf_document:
    keep_tex: true
    extra_dependencies: ["graphicx", "float", "amsmath"]
icfes:
  competencia: interpretacion_representacion
  nivel_dificultad: 2
  contenido:
    categoria: estadistica
    tipo: no_generico
  contexto: familiar
  eje_axial: eje4
  componente: aleatorio
---

```{r setup, include=FALSE}
# Configuración para todos los formatos de salida
Sys.setlocale(category = "LC_NUMERIC", locale = "C")
options(OutDec = ".")

library(exams)
library(ggplot2)
library(knitr)
library(testthat)

typ <- match_exams_device()
options(scipen = 999)
knitr::opts_chunk$set(
  warning = FALSE,
  message = FALSE,
  echo = FALSE,
  fig.showtext = FALSE,
  fig.cap = "",
  fig.keep = 'all',
  dev = c("png", "pdf"),
  dpi = 150,
  fig.pos = "H"
)
```

```{r DefinicionDeVariables, message=FALSE, warning=FALSE, results='asis'}
options(OutDec = ".")  # Asegurar punto decimal en este chunk

# Establecer semilla aleatoria para reproducibilidad
set.seed(sample(1:10000, 1))

# Aleatorización de contexto y personajes
nombres_pares <- list(
  c("Pedro", "Juan"), c("<PERSON>", "Luis"), c("Miguel", "José"),
  c("Antonio", "<PERSON>"), c("Diego", "<PERSON>"), c("Sebastián", "<PERSON>")
)
nombres_seleccionados <- sample(nombres_pares, 1)[[1]]
nombre1 <- nombres_seleccionados[1]
nombre2 <- nombres_seleccionados[2]

# Aleatorización de contexto habitacional
tipos_vivienda <- c("apartamento", "casa", "hogar", "residencia")
tipo_vivienda <- sample(tipos_vivienda, 1)

# Generar consumo máximo posible (variable clave del problema)
consumos_maximos_posibles <- c(18, 20, 22, 25)
consumo_maximo <- sample(consumos_maximos_posibles, 1)

# Generar porcentajes objetivo que den resultados "limpios"
porcentajes_objetivo <- c(60, 65, 70, 75, 80, 85, 90)
porcentaje_junio <- sample(porcentajes_objetivo, 1)
consumo_junio <- round((porcentaje_junio * consumo_maximo) / 100)

# Generar datos de consumo mensual realistas
meses <- c("Abril", "Mayo", "Junio", "Julio", "Agosto", "Septiembre", "Octubre")
meses_abrev <- c("Abr", "May", "Jun", "Jul", "Ago", "Sep", "Oct")

# Consumos base realistas (junio será el valor calculado)
consumos_base <- c(
  sample(10:13, 1),  # Abril
  sample(11:14, 1),  # Mayo
  consumo_junio,     # Junio (valor clave)
  sample(10:13, 1),  # Julio
  sample(10:13, 1),  # Agosto
  sample(11:14, 1),  # Septiembre
  sample(12:15, 1)   # Octubre
)

# Asegurar que ningún consumo exceda el máximo menos 1
for(i in c(1,2,4,5,6,7)) {
  consumos_base[i] <- min(consumos_base[i], consumo_maximo - 1)
}

consumos <- consumos_base
names(consumos) <- meses

# Generar datos económicos aleatorios
cargos_fijos <- c(2500, 2700, 2800, 3000, 3200)
totales_factura <- c(14000, 14500, 15000, 15500, 16000)
cargo_fijo <- sample(cargos_fijos, 1)
total_factura <- sample(totales_factura, 1)

# Calcular respuesta correcta
porcentaje_correcto <- round((consumo_junio / consumo_maximo) * 100)

# Generar distractores plausibles y matemáticamente coherentes
distractores <- c()

# Distractor 1: Error conceptual - usar el valor absoluto como porcentaje
if(consumo_junio <= 100 && consumo_junio != porcentaje_correcto) {
  distractores <- c(distractores, consumo_junio)
}

# Distractor 2: Error de cálculo - usar otro mes
meses_alternativos <- which(names(consumos) != "Junio")
mes_alternativo <- sample(meses_alternativos, 1)
porcentaje_alternativo <- round((consumos[mes_alternativo] / consumo_maximo) * 100)
if(porcentaje_alternativo != porcentaje_correcto && !porcentaje_alternativo %in% distractores) {
  distractores <- c(distractores, porcentaje_alternativo)
}

# Distractor 3: Error matemático común - denominador incorrecto
denominador_error <- consumo_maximo - sample(2:5, 1)
porcentaje_error <- round((consumo_junio / denominador_error) * 100)
if(porcentaje_error <= 100 && porcentaje_error != porcentaje_correcto && !porcentaje_error %in% distractores) {
  distractores <- c(distractores, porcentaje_error)
}

# Completar distractores con opciones plausibles si es necesario
opciones_adicionales <- c(15, 25, 35, 45, 55, 65, 85, 95, 100)
opciones_adicionales <- opciones_adicionales[opciones_adicionales != porcentaje_correcto]

for(opcion in sample(opciones_adicionales)) {
  if(length(distractores) >= 3) break
  if(!opcion %in% distractores) {
    distractores <- c(distractores, opcion)
  }
}

# Asegurar exactamente 3 distractores
distractores <- distractores[1:3]

# Crear opciones finales
opciones <- c(porcentaje_correcto, distractores)
opciones_texto <- paste0(opciones, "%")

# Vector de solución para r-exams
solucion <- c(TRUE, FALSE, FALSE, FALSE)

# Mezclar opciones aleatoriamente
orden_aleatorio <- sample(1:4)
opciones_mezcladas <- opciones_texto[orden_aleatorio]
solucion_mezclada <- solucion[orden_aleatorio]

# Detectar formato de salida para ajustes posteriores
formatos_moodle <- c("exams2moodle", "exams2qti12", "exams2qti21", "exams2openolat")
es_moodle <- (match_exams_call() %in% formatos_moodle)

# PRUEBAS DE VALIDACIÓN MATEMÁTICA
test_that("Validación de datos generados", {
  expect_true(consumo_maximo %in% c(18, 20, 22, 25))
  expect_true(consumo_junio > 0 && consumo_junio <= consumo_maximo)
  expect_true(porcentaje_correcto >= 50 && porcentaje_correcto <= 100)
  expect_true(all(consumos > 0 & consumos <= consumo_maximo))
  expect_equal(length(opciones), 4)
  expect_equal(length(unique(opciones)), 4)  # Todas las opciones deben ser diferentes
})

test_that("Validación de coherencia matemática", {
  # Verificar que el cálculo del porcentaje es correcto
  porcentaje_calculado <- round((consumo_junio / consumo_maximo) * 100)
  expect_equal(porcentaje_correcto, porcentaje_calculado)

  # Verificar que la respuesta correcta está en las opciones
  expect_true(porcentaje_correcto %in% opciones)

  # Verificar que los distractores son diferentes de la respuesta correcta
  expect_true(all(distractores != porcentaje_correcto))
})
```

```{r crear_grafico, echo=FALSE, fig.width=8, fig.height=5}
# Preparar datos para el gráfico
datos_grafico <- data.frame(
  Mes = factor(meses, levels = meses),
  Consumo = consumos
)

# Configurar colores - destacar junio
colores_barras <- rep("#4CAF50", length(meses))
colores_barras[which(meses == "Junio")] <- "#2E7D32"  # Verde más oscuro para junio

# Crear gráfico con ggplot2
grafico <- ggplot(datos_grafico, aes(x = Mes, y = Consumo)) +
  geom_bar(stat = "identity", fill = colores_barras, color = "black", width = 0.7) +
  labs(
    title = "",
    x = "Mes",
    y = "Consumo en metros cúbicos"
  ) +
  theme_minimal() +
  theme(
    axis.text.x = element_text(angle = 45, hjust = 1, size = 10),
    axis.text.y = element_text(size = 10),
    axis.title = element_text(size = 11, face = "bold"),
    panel.grid.major.x = element_blank(),
    panel.grid.minor = element_blank(),
    plot.margin = margin(10, 10, 10, 10),
    plot.title = element_text(hjust = 0.5)
  ) +
  scale_y_continuous(
    breaks = seq(0, max(consumos) + 2, by = 1),
    limits = c(0, max(consumos) + 1),
    expand = c(0, 0)
  )

# Mostrar el gráfico
print(grafico)

# Validar que el gráfico se creó correctamente
test_that("Validación del gráfico", {
  expect_true(exists("grafico"))
  expect_true("ggplot" %in% class(grafico))
  expect_equal(nrow(datos_grafico), 7)  # 7 meses
})
```

Question
========

`r nombre1` y `r nombre2` viven en un `r tipo_vivienda` y comparten el pago de los gastos. En octubre, el consumo de gas natural se facturó por $`r format(total_factura, big.mark = ".", decimal.mark = ",")`$, incluido el cargo fijo de $`r format(cargo_fijo, big.mark = ".", decimal.mark = ",")`$, que es el mismo todos los meses. La gráfica muestra el consumo histórico en metros cúbicos de ese servicio.

```{r mostrar_grafico, echo=FALSE, results='asis', fig.align="center"}
# Ajustar tamaño del gráfico según el formato de salida
if (es_moodle) {
  cat("El gráfico muestra el consumo mensual:\n\n")
} else {
  cat("La gráfica muestra el consumo histórico en metros cúbicos:\n\n")
}
```

Un hogar puede consumir máximo `r consumo_maximo` metros cúbicos en un mes. ¿A qué porcentaje del consumo máximo posible corresponde el consumo de junio?

Answerlist
----------
* `r opciones_mezcladas[1]`
* `r opciones_mezcladas[2]`
* `r opciones_mezcladas[3]`
* `r opciones_mezcladas[4]`

Solution
========

### Análisis del problema

Este problema requiere **interpretación de gráficos de barras** y **cálculo de porcentajes**. Los pasos son:

1. **Leer correctamente la gráfica** para identificar el consumo de junio
2. **Identificar el consumo máximo posible** según el enunciado
3. **Aplicar la fórmula de porcentaje** para encontrar la relación

### Paso 1: Lectura de la gráfica

Observando la gráfica de consumo histórico de `r nombre1` y `r nombre2`:

```{r analisis_datos, echo=FALSE, results='asis'}
cat("**Consumos mensuales registrados:**\n\n")
for(i in 1:length(meses)) {
  cat(paste0("- ", meses[i], ": ", consumos[i], " metros cúbicos\n"))
}
cat("\n")
```

**El consumo de junio fue de `r consumo_junio` metros cúbicos.**

### Paso 2: Datos del problema

- **Consumo máximo posible:** `r consumo_maximo` metros cúbicos (dato del enunciado)
- **Consumo real de junio:** `r consumo_junio` metros cúbicos (dato de la gráfica)

### Paso 3: Aplicación de la fórmula de porcentaje

Para calcular qué porcentaje representa el consumo de junio respecto al máximo posible:

$$\text{Porcentaje} = \frac{\text{Valor observado}}{\text{Valor máximo}} \times 100\%$$

Sustituyendo los valores:

$$\text{Porcentaje} = \frac{`r consumo_junio`}{`r consumo_maximo`} \times 100\% = `r porcentaje_correcto`\%$$

### Verificación y análisis de distractores

**Respuesta correcta:** `r porcentaje_correcto`%

```{r analisis_distractores, echo=FALSE, results='asis'}
cat("**Análisis de errores conceptuales comunes:**\n\n")

# Identificar qué tipo de distractores se generaron
for(i in 1:length(distractores)) {
  distractor <- distractores[i]

  if(distractor == consumo_junio && consumo_junio <= 100) {
    cat(paste0("- **", distractor, "%**: Error conceptual - confundir el valor absoluto (", consumo_junio, " m³) con el porcentaje\n"))
  } else if(distractor %in% round((consumos / consumo_maximo) * 100)) {
    mes_error <- names(consumos)[which(round((consumos / consumo_maximo) * 100) == distractor)][1]
    cat(paste0("- **", distractor, "%**: Error de lectura - usar el consumo de ", mes_error, " en lugar de junio\n"))
  } else {
    cat(paste0("- **", distractor, "%**: Error de cálculo - aplicar incorrectamente la fórmula de porcentaje\n"))
  }
}
```

### Conclusión

El consumo de junio (`r consumo_junio` metros cúbicos) representa el **`r porcentaje_correcto`%** del consumo máximo posible (`r consumo_maximo` metros cúbicos).

Esta respuesta es coherente porque:
- Se basa en una lectura correcta de la gráfica
- Aplica correctamente la fórmula de porcentaje
- El resultado está dentro del rango esperado (0% a 100%)

```{r solucion, echo=FALSE, results="asis"}
answerlist(ifelse(solucion_mezclada, "Correcto", "Incorrecto"), markup = "markdown")
```

Meta-information
================
exname: Consumo Gas Natural Porcentaje Máximo
extype: schoice
exsolution: `r paste(as.integer(solucion_mezclada), collapse="")`
exshuffle: TRUE
exsection: Estadística|Interpretación de gráficos|Porcentajes|Análisis de datos
exextra[Type]: Cálculo
exextra[Program]: R
exextra[Language]: es
exextra[Level]: 2
exextra[Competencia]: Interpretación y representación
exextra[Componente]: Aleatorio y sistemas de datos
exextra[Contexto]: Familiar
exextra[Dificultad]: Media
